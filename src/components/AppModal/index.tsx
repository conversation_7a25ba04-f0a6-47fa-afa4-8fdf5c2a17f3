import React, { useEffect } from "react";
import { Modal } from "antd";
import { CloseOutlined } from "@ant-design/icons";

const AppModal = ({
  isModalOpen,
  setIsModalOpen,
  title,
  children,
  onCancel,
}: {
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  title: string;
  children: React.ReactNode;
  onCancel?: () => void;
}) => {
  useEffect(() => {
    if (isModalOpen) {
      const scrollY = window.scrollY;
      document.body.style.position = "fixed";
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = "100%";
      document.body.style.overflow = "hidden";

      return () => {
        document.body.style.position = "";
        document.body.style.top = "";
        document.body.style.width = "";
        document.body.style.overflow = "";
        window.scrollTo(0, scrollY);
      };
    }
  }, [isModalOpen]);
  return (
    <Modal
      title={title}
      open={isModalOpen}
      onCancel={() => {
        setIsModalOpen(false);
        onCancel && onCancel();
      }}
      okButtonProps={{ style: { display: "none" } }}
      cancelButtonProps={{ style: { display: "none" } }}
      closeIcon={<CloseOutlined style={{ color: "white", fontSize: "16px" }} />}
      centered
      style={{
        maxHeight: "90vh",
        maxWidth: "90vw",
      }}
      styles={{
        body: {
          maxHeight: "70vh",
          overflowY: "auto",
          padding: "10px",
        },
      }}
    >
      {children}
    </Modal>
  );
};

export default AppModal;
