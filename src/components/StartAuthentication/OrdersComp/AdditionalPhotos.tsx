import React from "react";
import { useRouter } from "next/router";
import moment from "moment";
import { Modal } from "antd";

import { IServiceRequestAdditional } from "types/orders";
import apiCore from "utils/apiCore";
import { ordersPath } from "../constant";
import { showErrorPopupMessage } from "utils/message";
import { parseError } from "utils/error";

const AdditionalPhotos = ({
  id,
  accessToken,
  serviceRequestAdditionalPending,
}: {
  id: string;
  accessToken: string;
  serviceRequestAdditionalPending: IServiceRequestAdditional[];
}) => {
  const router = useRouter();

  const handleCancel = async (additionalId: number) => {
    setModalOpen(true);
    // Modal.confirm({
    //   title: "Confirm to cancel",
    //   content: "Confirm to cancel the authentication request",
    //   okText: "Confirm",
    //   okButtonProps: {
    //     style: {
    //       backgroundColor: "#ef4444",
    //       color: "#ffffff",
    //     },
    //   },
    //   cancelButtonProps: {
    //     style: {
    //       backgroundColor: "#363a42",
    //       borderColor: "#363a42",
    //       color: "#ffffff",
    //     },
    //   },
    //   styles: {
    //     body: {
    //       backgroundColor: "#1f222a",
    //       color: "#ffffff !important",
    //     },
    //     content: {
    //       backgroundColor: "#1f222a",
    //       color: "#ffffff !important",
    //     },
    //     header: {
    //       backgroundColor: "#1f222a",
    //       color: "#ffffff !important",
    //     },
    //     mask: {
    //       backgroundColor: "rgba(0, 0, 0, 0.45)",
    //     },
    //   },
    //   className: "custom-modal-confirm",
    //   onOk: async () => {
    //     try {
    //       await apiCore.patch(
    //         null,
    //         `v1/service_request/${additionalId}/cancel`,
    //         {},
    //         accessToken
    //       );
    //       router.push(ordersPath);
    //     } catch (error) {
    //       showErrorPopupMessage(parseError(error).message);
    //     }
    //   },
    // });
  };

  const handleSubmitAdditionalPhotos = (additionalId: number) => {
    router.push(`/portal/orders/${id}/additional-photos/${additionalId}`);
  };
  return (
    <div className="space-y-6">
      {serviceRequestAdditionalPending.map((additional) => (
        <div key={additional.id} className="rounded-lg p-4 space-y-2">
          <div className="flex justify-between items-start">
            <div className="text-red-500 font-bold">Additional Photos</div>
            <div className="text-right text-xs">
              <div className="capitalize">{additional.status}</div>
              <div>
                {moment(additional.created_at).format("DD MMM YYYY, h:mm A")}
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="text-gray-100 text-sm">
            Please follow the instructions to continue the authentication
            process.
          </div>

          {/* Action buttons */}
          <div className="space-y-3">
            <div className="md:max-w-md m-auto mt-6">
              <button
                onClick={() => handleSubmitAdditionalPhotos(additional.id)}
                className="w-full bg-red-500 hover:bg-red-600 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
              >
                <img
                  src="/order/icon-upload.png"
                  className="w-8 h-8"
                  alt="upload icon"
                />
                SUBMIT ADDITIONAL PHOTOS
              </button>
            </div>

            <div className="text-gray-100 text-sm">
              Cancel this authentication request and get a $LEGIT refund.
            </div>

            <div className="md:max-w-md m-auto mt-6">
              <button
                onClick={() => handleCancel(additional.id)}
                className="w-full bg-red-500 hover:bg-red-600 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
              >
                <img
                  src="/order/icon-clear.png"
                  className="w-8 h-8"
                  alt="cancel request icon"
                />
                CANCEL REQUEST
              </button>
            </div>
          </div>

          {/* Additional remark */}
          {additional.checker_additional_remark && (
            <div className="bg-dark-100 rounded-lg p-3">
              <div className="text-gray-400 text-sm font-medium mb-2">
                Additional Instructions:
              </div>
              <div className="text-gray-300 text-sm">
                {additional.checker_additional_remark}
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default AdditionalPhotos;
