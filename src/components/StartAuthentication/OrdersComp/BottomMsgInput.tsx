import React, { useState } from "react";

const BottomMsgInput = ({
  handleSendMessage,
}: {
  handleSendMessage: (msg: string) => void;
}) => {
  const [msg, setMsg] = useState<string>("");
  const handleMsgSend = () => {
    if (!msg.trim()) return;
    handleSendMessage(msg);
    setMsg("");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleMsgSend();
    }
  };

  return (
    <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 z-10 bg-dark-200 w-full max-w-screen-lg">
      <div className="h-20 md:px-[44px] px-4 flex justify-between items-center">
        <input
          id="password"
          type="text"
          value={msg}
          onChange={(e) => setMsg(e.target.value)}
          onKeyDown={handleKeyDown}
          className="outline-none w-4/5 bg-dark-300 rounded-lg px-4 py-2"
          required
          placeholder="Talk to our authenticators"
        />
        <div className="w-10 cursor-pointer" onClick={handleMsgSend}>
          <img src="/order/icon_msg_send.png" className="w-full object-cover" />
        </div>
      </div>
    </div>
  );
};

export default BottomMsgInput;
